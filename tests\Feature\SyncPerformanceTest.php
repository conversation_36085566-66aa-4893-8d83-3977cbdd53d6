<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SyncPerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test authentication
        putenv('API_USERNAME=test_user');
        putenv('API_PASSWORD=test_password');
    }

    protected function getAuthHeaders(): array
    {
        $credentials = base64_encode('test_user:test_password');
        return ['Authorization' => 'Basic ' . $credentials];
    }

    public function test_batch_operations_performance()
    {
        // Generate large dataset
        $tickets = [];
        $mealStubComponents = [];
        $currencyDetails = [];

        for ($i = 1; $i <= 1000; $i++) {
            $tickets[] = [
                'ticket_no' => "T{$i}",
                'branch_id' => rand(1, 10),
                'outlet_id' => rand(1, 5),
                'status' => 'S',
                'expiry' => now()->addDays(7)->toDateString()
            ];

            $mealStubComponents[] = [
                'REFERENCEID' => "MSC{$i}",
                'branch_id' => rand(1, 10),
                'outlet_id' => rand(1, 5),
                'product_id' => "P{$i}"
            ];

            $currencyDetails[] = [
                'INPARKCURRENCYID' => "IPC{$i}",
                'branch_id' => rand(1, 10),
                'outlet_id' => rand(1, 5),
                'STATUS' => 'ACTIVE',
                'product_id' => "P{$i}"
            ];
        }

        $data = [
            'tickets' => $tickets,
            'mealstubcomponents' => $mealStubComponents,
            'inparkcurrencydetails' => $currencyDetails
        ];

        // Measure performance
        $startTime = microtime(true);
        
        $response = $this->postJson('/api/sync/send-to-server', $data, $this->getAuthHeaders());
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $response->assertStatus(200);

        // Verify all records were inserted
        $this->assertEquals(1000, DB::table('tickets')->count());
        $this->assertEquals(1000, DB::table('mealstubcomponents')->count());
        $this->assertEquals(1000, DB::table('inparkcurrencydetails')->count());

        // Performance assertion - should complete within reasonable time
        $this->assertLessThan(10, $executionTime, "Batch operation took too long: {$executionTime} seconds");

        echo "\nBatch operation performance: {$executionTime} seconds for 3000 records\n";
    }

    public function test_caching_performance()
    {
        // Insert test data
        for ($i = 1; $i <= 100; $i++) {
            DB::table('tickets')->insert([
                'ticket_no' => "T{$i}",
                'branch_id' => 1,
                'outlet_id' => 1,
                'status' => 'S',
                'expiry' => now()->addDays(7)
            ]);
        }

        // First request (no cache)
        $startTime = microtime(true);
        $response1 = $this->getJson('/api/sync/get-from-server/1/1', $this->getAuthHeaders());
        $firstRequestTime = microtime(true) - $startTime;

        $response1->assertStatus(200);

        // Second request (with cache)
        $startTime = microtime(true);
        $response2 = $this->getJson('/api/sync/get-from-server/1/1', $this->getAuthHeaders());
        $secondRequestTime = microtime(true) - $startTime;

        $response2->assertStatus(200);

        // Cached request should be significantly faster
        $this->assertLessThan($firstRequestTime, $secondRequestTime);

        echo "\nCaching performance improvement: " . 
             "First request: {$firstRequestTime}s, " . 
             "Cached request: {$secondRequestTime}s\n";
    }

    public function test_concurrent_ticket_claiming()
    {
        // Insert test ticket
        DB::table('tickets')->insert([
            'ticket_no' => 'T001',
            'branch_id' => 1,
            'outlet_id' => 1,
            'status' => 'S',
            'expiry' => now()->addDays(7)
        ]);

        $data = [
            'ticket_no' => 'T001',
            'branch_id' => 1,
            'outlet_id' => 1
        ];

        // First claim should succeed
        $response1 = $this->postJson('/api/sync/claim', $data, $this->getAuthHeaders());
        $response1->assertStatus(200);

        // Second claim should fail (ticket already used)
        $response2 = $this->postJson('/api/sync/claim', $data, $this->getAuthHeaders());
        $response2->assertStatus(400)
                  ->assertJson(['error' => 'Ticket already used']);

        // Verify ticket is marked as used
        $this->assertDatabaseHas('tickets', [
            'ticket_no' => 'T001',
            'status' => 'U'
        ]);
    }
}
