<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SyncControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test authentication
        config(['app.env' => 'testing']);
        putenv('API_USERNAME=test_user');
        putenv('API_PASSWORD=test_password');
    }

    protected function getAuthHeaders(): array
    {
        $credentials = base64_encode('test_user:test_password');
        return ['Authorization' => 'Basic ' . $credentials];
    }

    public function test_send_to_server_requires_authentication()
    {
        $response = $this->postJson('/api/sync/send-to-server', []);
        
        $response->assertStatus(401)
                 ->assertJson(['error' => 'Authorization header required']);
    }

    public function test_send_to_server_with_valid_data()
    {
        $data = [
            'tickets' => [
                [
                    'ticket_no' => 'T001',
                    'branch_id' => 1,
                    'outlet_id' => 1,
                    'status' => 'S',
                    'expiry' => now()->addDays(7)->toDateString()
                ]
            ],
            'mealstubcomponents' => [
                [
                    'REFERENCEID' => 'MSC001',
                    'branch_id' => 1,
                    'outlet_id' => 1,
                    'product_id' => 'P001'
                ]
            ]
        ];

        $response = $this->postJson('/api/sync/send-to-server', $data, $this->getAuthHeaders());
        
        $response->assertStatus(200)
                 ->assertJson(['success' => 'Data synchronized successfully']);

        // Verify data was inserted
        $this->assertDatabaseHas('tickets', [
            'ticket_no' => 'T001',
            'branch_id' => 1,
            'outlet_id' => 1,
            'status' => 'S'
        ]);

        $this->assertDatabaseHas('mealstubcomponents', [
            'REFERENCEID' => 'MSC001',
            'branch_id' => 1,
            'outlet_id' => 1,
            'product_id' => 'P001'
        ]);
    }

    public function test_get_from_server_with_caching()
    {
        // Insert test data
        DB::table('tickets')->insert([
            'ticket_no' => 'T001',
            'branch_id' => 1,
            'outlet_id' => 1,
            'status' => 'S',
            'expiry' => now()->addDays(7)
        ]);

        $response = $this->getJson('/api/sync/get-from-server/1/1', $this->getAuthHeaders());
        
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'tickets',
                     'meal_stub_components',
                     'in_park_currency_details',
                     'last_updated'
                 ]);

        // Verify caching works
        $this->assertTrue(Cache::has('sync_data_1_1'));
    }

    public function test_claim_ticket_success()
    {
        // Insert test ticket
        DB::table('tickets')->insert([
            'ticket_no' => 'T001',
            'branch_id' => 1,
            'outlet_id' => 1,
            'status' => 'S',
            'expiry' => now()->addDays(7)
        ]);

        $data = [
            'ticket_no' => 'T001',
            'branch_id' => 1,
            'outlet_id' => 1
        ];

        $response = $this->postJson('/api/sync/claim', $data, $this->getAuthHeaders());
        
        $response->assertStatus(200)
                 ->assertJson(['message' => 'Ticket claimed successfully']);

        // Verify ticket status was updated
        $this->assertDatabaseHas('tickets', [
            'ticket_no' => 'T001',
            'status' => 'U'
        ]);
    }

    public function test_claim_nonexistent_ticket()
    {
        $data = [
            'ticket_no' => 'NONEXISTENT',
            'branch_id' => 1,
            'outlet_id' => 1
        ];

        $response = $this->postJson('/api/sync/claim', $data, $this->getAuthHeaders());
        
        $response->assertStatus(404)
                 ->assertJson(['error' => 'Ticket not found']);
    }

    public function test_void_expired_tickets()
    {
        // Insert expired ticket
        DB::table('tickets')->insert([
            'ticket_no' => 'T001',
            'branch_id' => 1,
            'outlet_id' => 1,
            'status' => 'S',
            'expiry' => now()->subDays(1)
        ]);

        $response = $this->postJson('/api/sync/void-expired', [], $this->getAuthHeaders());
        
        $response->assertStatus(200)
                 ->assertJsonStructure(['message', 'voided_count']);

        // Verify ticket was voided
        $this->assertDatabaseHas('tickets', [
            'ticket_no' => 'T001',
            'status' => 'V'
        ]);
    }

    public function test_validation_errors()
    {
        $invalidData = [
            'tickets' => [
                [
                    'ticket_no' => '', // Invalid: empty
                    'branch_id' => 'invalid', // Invalid: not integer
                    'status' => 'INVALID' // Invalid: not in allowed values
                ]
            ]
        ];

        $response = $this->postJson('/api/sync/send-to-server', $invalidData, $this->getAuthHeaders());
        
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['tickets.0.ticket_no', 'tickets.0.branch_id', 'tickets.0.status']);
    }

    public function test_rate_limiting()
    {
        // Make multiple requests to test rate limiting
        for ($i = 0; $i < 65; $i++) {
            $response = $this->getJson('/api/sync/get-from-server/1/1', $this->getAuthHeaders());
            
            if ($i < 60) {
                $response->assertStatus(200);
            } else {
                $response->assertStatus(429); // Too Many Requests
                break;
            }
        }
    }
}
