<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class SyncController extends Controller
{
    public function sendToServer(Request $request)
    {
        $branchId = $request->input('BRANCHID');
        $outletId = $request->input('OUTLETID');

        // 1. Kunin lahat ng NEW/VOID status per branch at outlet
        $tickets = DB::table('tickets')
            ->where('TICKETNO', $tickets)
            ->where('TYPE', $tickets)
            ->where('POSNO', $tickets)
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V']) //U= USED // VOID
            ->get();

        $mealStub = DB::table('mealstubcomponents')
            ->where('REFERENCEID', $mealStub)
            ->where('LINENO', $mealStub)
            ->where('PRODUCTID', $mealStub)
            ->where('POSTMIXID', $mealStub)
            ->whereIn('rowguid', Str::uuid())
            ->get();

        $currencyDetails = DB::table('inparkcurrencydetails')
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        $trxHeader = DB::table('TransactionHeader')
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        $trxDetails = DB::table('TransactionDetails')
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        // 2. Payload
        $payload = [
            'BRANCHID' => $branchId,
            'OUTLETID' => $outletId,
            'tickets' => $tickets,
            'meal_stub' => $mealStub,
            'currency_details' => $currencyDetails,
            'transaction_header' => $trxHeader,
            'transaction_details' => $trxDetails,
        ];

        // 3. Send to central server
        $response = Http::post('http://central-server.local/api/receive-transactions', $payload);

        // 4. Pag success, update local DB
        if ($response->successful()) {
            $this->sendToDBUpdateInternal(
                $branchId,
                $outletId,
                $tickets,
                $mealStub,
                $currencyDetails,
                $trxHeader,
                $trxDetails
            );
            return response()->json(['message' => 'Data sent and updated successfully.']);
        }

        return response()->json(['message' => 'Failed to send data to server.'], 500);
    }

    public function sendToDBUpdate(Request $request)
    {
        $branchId = $request->input('branch_id');
        $outletId = $request->input('outlet_id');

        $this->sendToDBUpdateInternal($branchId, $outletId);

        return response()->json(['message' => 'Local DB updated successfully.']);
    }

    private function sendToDBUpdateInternal($branchId, $outletId, $tickets = null, $mealStub = null, $currencyDetails = null, $trxHeader = null, $trxDetails = null)
    {
        if (is_null($tickets)) {
            $tickets = DB::table('tickets')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $mealStub = DB::table('mealstubcomponents')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $currencyDetails = DB::table('inparkcurrencydetails')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $trxHeader = DB::table('TransactionHeader')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $trxDetails = DB::table('TransactionDetails')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();
        }

        // Update statuses to SENT
        if ($tickets->count()) {
            DB::table('tickets')->whereIn('id', $tickets->pluck('id'))->update(['STATUS' => 'SENT']);
        }
        if ($mealStub->count()) {
            DB::table('mealstubcomponents')->whereIn('id', $mealStub->pluck('id'))->update(['STATUS' => 'SENT']);
        }
        if ($currencyDetails->count()) {
            DB::table('inparkcurrencydetails')->whereIn('id', $currencyDetails->pluck('id'))->update(['STATUS' => 'SENT']);
        }
        if ($trxHeader->count()) {
            DB::table('TransactionHeader')->whereIn('id', $trxHeader->pluck('id'))->update(['STATUS' => 'SENT']);
        }
        if ($trxDetails->count()) {
            DB::table('TransactionDetails')->whereIn('id', $trxDetails->pluck('id'))->update(['STATUS' => 'SENT']);
        }
    }

    public function getFromServer($branchId, $outletId)
    {
        try {
            $response = Http::get("http://central-server.local/api/get-transactions/{$branchId}/{$outletId}");

            if ($response->successful()) {
                $data = $response->json();
                $this->saveReceivedData($data, $branchId, $outletId);

                return response()->json([
                    'message' => 'Data retrieved and saved successfully.',
                    'data' => $data
                ]);
            }

            return response()->json(['message' => 'Failed to retrieve data from server.'], 500);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }

    public function claim(Request $request)
    {
        try {
            $branchId = $request->input('BRANCHID');
            $outletId = $request->input('OUTLETID');
            $ticketIds = $request->input('ticket_ids', []);

            DB::table('tickets')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('id', $ticketIds)
                ->update(['STATUS' => 'CLAIMED']);

            return response()->json(['message' => 'Tickets claimed successfully.']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }

    public function voidExpired(Request $request)
    {
        try {
            $branchId = $request->input('BRANCHID');
            $outletId = $request->input('OUTLETID');
            $expiredDate = $request->input('expired_date', now()->subDays(30));

            $voidedCount = DB::table('tickets')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->where('created_at', '<', $expiredDate)
                ->whereIn('STATUS', ['U', 'V', 'SENT']) // U
                ->update(['STATUS' => 'VOID']);

            return response()->json([
                'message' => 'Expired tickets voided successfully.',
                'voided_count' => $voidedCount
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }

    private function saveReceivedData($data, $branchId, $outletId)
    {
        if (isset($data['tickets'])) {
            foreach ($data['tickets'] as $ticket) {
                DB::table('tickets')->updateOrInsert(
                    ['id' => $ticket['id']],
                    array_merge($ticket, ['STATUS' => 'RECEIVED'])
                );
            }
        }

        if (isset($data['meal_stub'])) {
            foreach ($data['meal_stub'] as $mealStub) {
                DB::table('mealstubcomponents')->updateOrInsert(
                    ['id' => $mealStub['id']],
                    array_merge($mealStub, ['STATUS' => 'RECEIVED'])
                );
            }
        }

        if (isset($data['currency_details'])) {
            foreach ($data['currency_details'] as $currency) {
                DB::table('inparkcurrencydetails')->updateOrInsert(
                    ['id' => $currency['id']],
                    array_merge($currency, ['STATUS' => 'RECEIVED'])
                );
            }
        }

        if (isset($data['transaction_header'])) {
            foreach ($data['transaction_header'] as $header) {
                DB::table('TransactionHeader')->updateOrInsert(
                    ['id' => $header['id']],
                    array_merge($header, ['STATUS' => 'RECEIVED'])
                );
            }
        }

        if (isset($data['transaction_details'])) {
            foreach ($data['transaction_details'] as $detail) {
                DB::table('TransactionDetails')->updateOrInsert(
                    ['id' => $detail['id']],
                    array_merge($detail, ['STATUS' => 'RECEIVED'])
                );
            }
        }
    }
}
