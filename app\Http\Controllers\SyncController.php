<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class SyncController extends Controller
{
    public function sendToServer(Request $request)
    {
        $branchId = $request->input('BRANCHID');
        $outletId = $request->input('OUTLETID');

        // 1. Kunin lahat ng NEW status per branch at outlet
        
        // Tickets kukunin
        $tickets = DB::table('tickets')
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        $mealStub = DB::table('mealstubcomponents')
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        $currencyDetails = DB::table('inparkcurrencydetails')
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        $trxHeader = DB::table('TransactionHeader')
            ->where('BRANCHID', $branchId)
            ->where('OUTLETID', $outletId)
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        $trxDetails = DB::table('TransactionDetails')
            ->whereIn('STATUS', ['U', 'V'])
            ->get();

        $redeemOutlet = DB::table('RedeemOutlet')
            ->where('rowguid', $redeemOutlet)
            ->where('BranchID', $branchId)
            ->where('OutletID', $outletId)
            ->get();

        // 2. Payload
        $payload = [
            'BRANCHID' => $branchId,
            'OUTLET' => $outletId,
            'tickets' => $tickets,
            'meal_stub' => $mealStub,
            'currency_details' => $currencyDetails,
            'transaction_header' => $trxHeader,
            'transaction_details' => $trxDetails,
            'redeem_outlet' => $redeemOutlet,
        ];

        // 3. Send to central server
        $response = Http::post('http://central-server.local/api/receive-transactions', $payload);

        // 4. Pag success, update local DB
        if ($response->successful()) {
            $this->sendToDBUpdateInternal(
                $branchId,
                $outletId,
                $tickets,
                $mealStub,
                $currencyDetails,
                $trxHeader,
                $trxDetails,
                $redeemOutlet
            );
            return response()->json(['message' => 'Data sent and updated successfully.']);
        }

        return response()->json(['message' => 'Failed to send data to server.'], 500);
    }

    public function sendToDBUpdate(Request $request)
    {
        $branchId = $request
        ->input('branch_id');
        $outletId = $request
        ->input('outlet_id');

        $this->sendToDBUpdateInternal($branchId, $outletId);
        return response()->json(['message' => 'Local DB updated successfully.']);
    }

    private function sendToDBUpdateInternal($branchId, $outletId, $tickets = null, $mealStub = null, $currencyDetails = null, $trxHeader = null, $trxDetails = null, $redeemOutlet = null)
    {

        if (is_null($tickets)) {
            $tickets = DB::table('tickets')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $mealStub = DB::table('mealstubcomponents')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $currencyDetails = DB::table('inparkcurrencydetails')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $trxHeader = DB::table('TransactionHeader')
                ->where('BRANCHID', $branchId)
                ->where('OUTLETID', $outletId)
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $trxDetails = DB::table('TransactionDetails')
                ->whereIn('STATUS', ['U', 'V'])
                ->get();

            $redeemOutlet = DB::table('RedeemOutlet')
            ->where('rowguid', $redeemOutlet)
            ->where('BranchID', $branchId)
            ->where('OutletID', $outletId)
            ->get();
        }

        // Update per branch & outlet
        DB::table('tickets')
            ->whereIn('id', $tickets->pluck('id'))
            ->update(['STATUS' => 'SENT']);

        DB::table('mealstubcomponents')
            ->whereIn('id', $mealStub->pluck('id'))
            ->update(['STATUS' => 'SENT']);

        DB::table('inparkcurrencydetails')
            ->whereIn('id', $currencyDetails->pluck('id'))
            ->update(['STATUS' => 'SENT']);

        DB::table('TransactionHeader')
            ->whereIn('id', $trxHeader->pluck('id'))
            ->update(['STATUS' => 'SENT']);

        DB::table('TransactionDetails')
            ->whereIn('id', $trxDetails->pluck('id'))
            ->update(['STATUS' => 'SENT']);

        DB::table('RedeemOutlet')
            ->whereIn('id', $redeemOutlet->pluck('id'))
            ->update(['STATUS' => 'SENT']);
    }
}










    

